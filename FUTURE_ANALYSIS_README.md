# 未来时间窗口分析功能说明

## 功能概述

本次增强为股票时间窗口累计涨跌幅分析功能添加了**未来时间窗口分析**能力，在现有历史时间窗口（7日、14日、30日）基础上，新增了三个前瞻性时间窗口：

- **未来30日时间窗口**
- **未来60日时间窗口**  
- **未来90日时间窗口**

## 核心特性

### 1. 双向时间窗口分析
- **历史时间窗口（回望）**：从指定日期向前回望的传统分析
- **未来时间窗口（前瞻）**：从指定日期向后前瞻的回测分析

### 2. 数据范围限制
- **年份限制**：未来时间窗口分析仅统计2024年及之前的历史数据
- **数据完整性**：排除2025年数据，确保未来窗口有完整的历史数据用于回测
- **交易日验证**：确保未来时间窗口的计算基于实际交易日

### 3. 一致的计算逻辑
- 保持与现有历史时间窗口相同的计算方法和展示格式
- 使用复合增长公式计算累计涨跌幅
- 提供相同的统计指标（波动率、夏普比率等）

## 技术实现

### 配置文件更新 (`config.py`)

```python
# 未来时间窗口配置（天数）
FUTURE_TIME_WINDOWS = [30, 60, 90]

# 数据年份限制配置
DATA_YEAR_LIMIT = {
    'future_analysis_max_year': 2024,  # 未来时间窗口分析的最大年份
    'exclude_current_year': True       # 是否排除当前年份（2025年）
}
```

### 核心类增强 (`src/time_window_analyzer.py`)

#### 新增方法：

1. **`_filter_data_for_future_analysis()`**
   - 过滤数据，仅保留2024年及之前的数据

2. **`_get_future_trading_days_in_window()`**
   - 获取未来时间窗口内的交易日（向后推算）

3. **`calculate_future_window_performance()`**
   - 计算未来时间窗口内的板块表现

4. **`calculate_multiple_future_windows()`**
   - 批量计算多个未来时间窗口的表现

5. **`calculate_combined_windows()`**
   - 组合计算历史和未来时间窗口分析

### 主程序增强 (`main.py`)

#### 新增命令行参数：

```bash
--enable-future-analysis    # 启用未来时间窗口分析
--future-windows 30,60,90   # 指定未来时间窗口（默认：30,60,90）
```

#### 参数示例：

```bash
# 启用未来分析的完整分析
python main.py --enable-future-analysis

# 自定义时间窗口的未来分析
python main.py --enable-future-analysis --windows 7,14 --future-windows 30,60,90

# 快速模式 + 未来分析
python main.py --quick-mode --enable-future-analysis --verbose

# 指定日期的未来分析
python main.py --enable-future-analysis --end-date 2023-06-15
```

### 报告生成增强 (`src/report_generator.py`)

- **HTML报告**：新增未来时间窗口分析部分
- **CSS样式**：添加专门的未来分析样式
- **数据展示**：明确区分历史回望窗口和未来前瞻窗口
- **说明文档**：在报告中添加分析说明和数据限制说明

## 使用方法

### 1. 基础使用

```bash
# 启用未来分析（使用默认配置）
python main.py --enable-future-analysis
```

### 2. 自定义配置

```bash
# 自定义历史和未来时间窗口
python main.py --enable-future-analysis \
  --windows 7,14,30 \
  --future-windows 30,60,90
```

### 3. 指定分析日期

```bash
# 以2023年6月15日为参考点进行分析
python main.py --enable-future-analysis \
  --end-date 2023-06-15
```

### 4. 快速模式

```bash
# 快速模式 + 未来分析 + 详细输出
python main.py --quick-mode --enable-future-analysis --verbose
```

## 输出结果

### 控制台输出

程序会分别显示：
1. **历史时间窗口分析结果**（回望分析）
2. **未来时间窗口分析结果**（前瞻分析）
3. **对比分析**（如果同时启用）

### HTML报告

生成的HTML报告包含：
- **历史时间窗口分析**部分
- **未来时间窗口分析**部分  
- **分析说明**和**数据限制**说明
- **美化的样式**区分不同类型的分析

### CSV导出

如果启用CSV导出，会额外生成：
- `future_window_performance_30d.csv`
- `future_window_performance_60d.csv`
- `future_window_performance_90d.csv`

## 应用场景

### 1. 策略回测
- 基于历史某个时点，验证策略在"未来"的表现
- 评估投资决策的预测准确性

### 2. 风险评估
- 分析板块在不同时间窗口下的表现一致性
- 识别具有持续性表现的板块

### 3. 趋势分析
- 对比历史回望和未来前瞻的结果
- 发现板块表现的趋势性特征

### 4. 投资决策支持
- 结合历史和未来分析，获得更全面的投资视角
- 提高投资决策的科学性

## 注意事项

### 1. 数据限制
- 未来分析基于≤2024年的历史数据
- 不包含2025年数据，确保回测完整性
- 需要足够的历史数据支持未来窗口计算

### 2. 分析局限
- 未来分析是基于历史数据的回测，不代表真实未来
- 结果仅供参考，不构成投资建议
- 市场环境变化可能影响分析有效性

### 3. 性能考虑
- 启用未来分析会增加计算时间
- 建议在快速模式下进行初步测试
- 大数据集分析时注意内存使用

## 测试验证

项目提供了多个测试脚本：

1. **`test_future_windows.py`** - 完整功能测试
2. **`demo_future_analysis.py`** - 功能演示
3. **`simple_future_test.py`** - 简单功能验证

运行测试：
```bash
python test_future_windows.py
python demo_future_analysis.py
python simple_future_test.py
```

## 技术支持

如有问题或需要进一步定制，请参考：
- 源代码注释
- 测试脚本示例
- HTML报告中的详细说明

---

**版本信息**：v1.0 - 未来时间窗口分析功能
**更新日期**：2025-07-23
**兼容性**：与现有功能完全兼容，向后兼容
