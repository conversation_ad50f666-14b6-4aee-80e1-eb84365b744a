#!/usr/bin/env python3
"""
演示默认启用的历史+未来分析功能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_default_analysis():
    """演示默认分析功能"""
    print("🎯 演示默认启用的历史+未来分析功能")
    print("=" * 70)
    print("📝 功能说明：")
    print("   现在用户只需运行 'python main.py' 就能自动获得：")
    print("   ✅ 历史时间窗口分析（7日、14日、30日）")
    print("   ✅ 未来时间窗口分析（30日、60日、90日）")
    print("   ✅ 所有其他分析功能（排名统计、历史同期分析等）")
    print()
    
    try:
        # 导入必要模块
        from src.data_loader import DataLoader
        from src.time_window_analyzer import TimeWindowAnalyzer
        import config
        
        # 显示配置信息
        print("🔧 当前配置：")
        print(f"   历史时间窗口: {config.TIME_WINDOWS}")
        print(f"   未来时间窗口: {config.FUTURE_TIME_WINDOWS}")
        print(f"   未来分析年份限制: ≤{config.DATA_YEAR_LIMIT['future_analysis_max_year']}年")
        print()
        
        # 加载演示数据
        print("📊 加载演示数据...")
        loader = DataLoader()
        loader.file_list = loader.file_list[:10]  # 只加载10个文件进行演示
        data = loader.load_all_data()
        print(f"   ✅ 数据加载完成: {data.shape}")
        
        # 初始化分析器
        print("\n🔧 初始化分析器...")
        analyzer = TimeWindowAnalyzer(data)
        print(f"   ✅ 分析器初始化完成")
        print(f"   全部数据范围: {analyzer.date_range[0]} 到 {analyzer.date_range[1]}")
        print(f"   未来分析数据范围: {analyzer.filtered_date_range[0]} 到 {analyzer.filtered_date_range[1]}")
        
        # 选择分析日期
        if not analyzer.filtered_available_dates:
            print("   ⚠️ 没有可用的过滤数据，跳过演示")
            return False
            
        # 选择一个2023年的日期作为演示
        demo_date = None
        for date in analyzer.filtered_available_dates:
            if date.year == 2023:
                demo_date = date
                break
        
        if not demo_date:
            demo_date = analyzer.filtered_available_dates[-50]  # 备选方案
            
        print(f"\n📅 演示分析日期: {demo_date.strftime('%Y-%m-%d')}")
        
        # 演示历史分析
        print(f"\n📈 历史时间窗口分析演示：")
        print("-" * 50)
        
        historical_results = {}
        for window_days in config.TIME_WINDOWS:
            try:
                result = analyzer.calculate_window_performance(demo_date, window_days)
                if not result.empty:
                    historical_results[window_days] = result
                    top_sector = result.iloc[0]
                    print(f"   历史{window_days:2d}日窗口最佳: {top_sector['sector_name']} "
                          f"({top_sector['cumulative_return_pct']:.2f}%)")
                else:
                    print(f"   历史{window_days:2d}日窗口: 无数据")
            except Exception as e:
                print(f"   历史{window_days:2d}日窗口: 错误 - {str(e)}")
        
        # 演示未来分析
        print(f"\n🔮 未来时间窗口分析演示：")
        print("-" * 50)
        
        future_results = {}
        for window_days in config.FUTURE_TIME_WINDOWS:
            try:
                result = analyzer.calculate_future_window_performance(demo_date, window_days)
                if not result.empty:
                    future_results[window_days] = result
                    top_sector = result.iloc[0]
                    print(f"   未来{window_days:2d}日窗口最佳: {top_sector['sector_name']} "
                          f"({top_sector['cumulative_return_pct']:.2f}%)")
                    
                    # 显示时间范围
                    start_date = top_sector['window_start_date']
                    end_date = top_sector['window_end_date']
                    print(f"       时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
                else:
                    print(f"   未来{window_days:2d}日窗口: 无数据")
            except Exception as e:
                print(f"   未来{window_days:2d}日窗口: 错误 - {str(e)}")
        
        # 统计信息
        total_historical = sum(len(df) for df in historical_results.values())
        total_future = sum(len(df) for df in future_results.values())
        
        print(f"\n📊 分析统计：")
        print(f"   历史分析结果: {len(historical_results)} 个时间窗口，{total_historical} 条记录")
        print(f"   未来分析结果: {len(future_results)} 个时间窗口，{total_future} 条记录")
        print(f"   总计: {len(historical_results) + len(future_results)} 个时间窗口")
        
        # 使用说明
        print(f"\n💡 使用说明：")
        print("-" * 50)
        print("   现在用户只需要运行以下命令：")
        print("   ")
        print("   🚀 python main.py")
        print("   ")
        print("   就能自动获得：")
        print("   ✅ 历史时间窗口分析（7日、14日、30日）")
        print("   ✅ 未来时间窗口分析（30日、60日、90日）")
        print("   ✅ 板块排名频次统计")
        print("   ✅ 单日冠军板块统计")
        print("   ✅ 历史同期分析")
        print("   ✅ HTML综合报告")
        print("   ✅ CSV数据导出")
        print()
        print("   🎯 无需学习新参数，无需额外配置！")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_before_after():
    """显示修改前后的对比"""
    print(f"\n📋 修改前后对比：")
    print("=" * 70)
    
    print("🔴 修改前（需要额外参数）：")
    print("   python main.py --enable-future-analysis --windows 7,14,30 --future-windows 30,60,90")
    print("   ❌ 用户需要学习新参数")
    print("   ❌ 容易忘记启用未来分析")
    print("   ❌ 命令行复杂")
    print()
    
    print("🟢 修改后（默认启用）：")
    print("   python main.py")
    print("   ✅ 自动包含所有分析功能")
    print("   ✅ 无需学习新参数")
    print("   ✅ 命令简单易用")
    print("   ✅ 默认获得最佳体验")

def main():
    """主函数"""
    print("🚀 开始演示默认启用的历史+未来分析功能")
    
    # 运行演示
    success = demo_default_analysis()
    
    if success:
        # 显示对比
        show_before_after()
        print("\n🎉 演示成功完成！")
        print("✨ 用户现在可以直接运行 'python main.py' 获得完整的分析结果！")
    else:
        print("\n💥 演示失败，请检查实现。")
        sys.exit(1)

if __name__ == "__main__":
    main()
