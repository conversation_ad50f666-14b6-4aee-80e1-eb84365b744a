#!/usr/bin/env python3
"""
未来时间窗口分析功能演示
展示如何使用新增的前瞻性分析功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_future_analysis():
    """演示未来时间窗口分析功能"""
    print("🔮 未来时间窗口分析功能演示")
    print("=" * 60)
    print("📝 功能说明：")
    print("   - 历史时间窗口：从指定日期向前回望的传统分析")
    print("   - 未来时间窗口：从指定日期向后前瞻的分析（基于历史数据回测）")
    print("   - 数据限制：未来分析仅使用2024年及之前的数据，确保回测完整性")
    print()
    
    try:
        # 导入必要的模块
        from src.data_loader import DataLoader
        from src.time_window_analyzer import TimeWindowAnalyzer
        import config
        import pandas as pd
        from datetime import datetime
        
        # 1. 加载演示数据
        print("1. 加载演示数据...")
        loader = DataLoader()
        # 加载前50个文件进行演示
        loader.file_list = loader.file_list[:50]
        data = loader.load_all_data()
        print(f"   ✓ 数据加载完成，形状: {data.shape}")
        
        # 2. 初始化分析器
        print("\n2. 初始化时间窗口分析器...")
        analyzer = TimeWindowAnalyzer(data)
        print(f"   ✓ 分析器初始化完成")
        print(f"   ✓ 全部数据范围: {analyzer.date_range[0]} 到 {analyzer.date_range[1]}")
        print(f"   ✓ 未来分析数据范围: {analyzer.filtered_date_range[0]} 到 {analyzer.filtered_date_range[1]}")
        
        # 3. 选择分析日期
        print("\n3. 选择分析参考日期...")
        # 选择一个2023年的日期作为参考点，这样可以看到"未来"的表现
        reference_date = None
        for date in analyzer.filtered_available_dates:
            if date.year == 2023 and date.month >= 6:  # 选择2023年中期的日期
                reference_date = date
                break
        
        if reference_date is None:
            reference_date = analyzer.filtered_available_dates[-100]  # 备选方案
            
        print(f"   选择的参考日期: {reference_date.strftime('%Y-%m-%d')}")
        
        # 4. 历史时间窗口分析演示
        print("\n4. 📊 历史时间窗口分析（回望分析）")
        print("-" * 50)
        
        historical_windows = config.TIME_WINDOWS
        historical_results = {}
        
        for window_days in historical_windows:
            performance = analyzer.calculate_window_performance(reference_date, window_days)
            if not performance.empty:
                historical_results[window_days] = performance
                top_3 = performance.head(3)
                
                print(f"\n📈 历史{window_days}日时间窗口 (前3名):")
                print(f"   时间范围: {reference_date - pd.Timedelta(days=window_days*1.5)} 到 {reference_date}")
                for i, (_, row) in enumerate(top_3.iterrows(), 1):
                    print(f"   {i}. {row['sector_name']:<12} {row['cumulative_return_pct']:>8.2f}% "
                          f"(波动率: {row['volatility']:>6.2f}%)")
        
        # 5. 未来时间窗口分析演示
        print(f"\n5. 🔮 未来时间窗口分析（前瞻分析，基于≤{config.DATA_YEAR_LIMIT['future_analysis_max_year']}年数据）")
        print("-" * 50)
        
        future_windows = config.FUTURE_TIME_WINDOWS
        future_results = {}
        
        for window_days in future_windows:
            performance = analyzer.calculate_future_window_performance(reference_date, window_days)
            if not performance.empty:
                future_results[window_days] = performance
                top_3 = performance.head(3)
                
                print(f"\n🚀 未来{window_days}日时间窗口 (前3名):")
                if not top_3.empty:
                    start_date = top_3.iloc[0]['window_start_date']
                    end_date = top_3.iloc[0]['window_end_date']
                    print(f"   时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
                    
                    for i, (_, row) in enumerate(top_3.iterrows(), 1):
                        print(f"   {i}. {row['sector_name']:<12} {row['cumulative_return_pct']:>8.2f}% "
                              f"(波动率: {row['volatility']:>6.2f}%)")
        
        # 6. 对比分析
        print("\n6. 📊 历史 vs 未来对比分析")
        print("-" * 50)
        
        # 找到共同的时间窗口进行对比
        common_windows = set(historical_results.keys()) & set(future_results.keys())
        
        if common_windows:
            print("   时间窗口对比 (最佳板块表现):")
            print(f"   {'窗口':<8} {'历史最佳':<15} {'历史收益率':<10} {'未来最佳':<15} {'未来收益率':<10}")
            print("   " + "-" * 70)
            
            for window_days in sorted(common_windows):
                hist_best = historical_results[window_days].iloc[0]
                future_best = future_results[window_days].iloc[0]
                
                print(f"   {window_days}日     {hist_best['sector_name']:<15} "
                      f"{hist_best['cumulative_return_pct']:>8.2f}%  "
                      f"{future_best['sector_name']:<15} "
                      f"{future_best['cumulative_return_pct']:>8.2f}%")
        
        # 7. 一致性分析
        print("\n7. 🎯 一致性分析")
        print("-" * 50)
        
        if common_windows:
            consistent_performers = []
            
            for window_days in sorted(common_windows):
                hist_top5 = set(historical_results[window_days].head(5)['sector_code'])
                future_top5 = set(future_results[window_days].head(5)['sector_code'])
                
                intersection = hist_top5 & future_top5
                consistency_rate = len(intersection) / 5 * 100
                
                print(f"   {window_days}日窗口一致性: {consistency_rate:.1f}% ({len(intersection)}/5个板块)")
                
                if intersection:
                    consistent_sectors = []
                    for sector_code in intersection:
                        hist_row = historical_results[window_days][
                            historical_results[window_days]['sector_code'] == sector_code
                        ].iloc[0]
                        consistent_sectors.append(hist_row['sector_name'])
                    
                    print(f"     一致表现板块: {', '.join(consistent_sectors)}")
        
        # 8. 使用建议
        print("\n8. 💡 使用建议")
        print("-" * 50)
        print("   📈 历史时间窗口分析：")
        print("     - 适用于回顾性分析和趋势确认")
        print("     - 帮助理解板块在特定时期的表现")
        print("     - 用于验证投资策略的历史有效性")
        print()
        print("   🔮 未来时间窗口分析：")
        print("     - 基于历史数据的前瞻性回测")
        print("     - 帮助评估策略的预测能力")
        print("     - 用于风险评估和投资决策参考")
        print()
        print("   🎯 组合使用：")
        print("     - 结合历史和未来分析，获得更全面的视角")
        print("     - 关注一致性表现的板块，可能具有更强的趋势性")
        print("     - 注意数据限制，未来分析基于历史数据，不代表真实未来")
        
        print("\n✅ 演示完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def show_usage_examples():
    """显示使用示例"""
    print("\n📚 使用示例")
    print("=" * 60)
    
    print("1. 启用未来分析的完整分析：")
    print("   python main.py --enable-future-analysis")
    print()
    
    print("2. 自定义时间窗口的未来分析：")
    print("   python main.py --enable-future-analysis --windows 7,14 --future-windows 30,60,90")
    print()
    
    print("3. 快速模式 + 未来分析：")
    print("   python main.py --quick-mode --enable-future-analysis --verbose")
    print()
    
    print("4. 指定日期的未来分析：")
    print("   python main.py --enable-future-analysis --end-date 2023-06-15")
    print()
    
    print("📋 参数说明：")
    print("   --enable-future-analysis  : 启用未来时间窗口分析")
    print("   --future-windows         : 指定未来时间窗口（默认: 30,60,90）")
    print("   --windows               : 指定历史时间窗口（默认: 7,14,30）")
    print("   --end-date              : 指定分析参考日期")

if __name__ == "__main__":
    print("🚀 开始未来时间窗口分析功能演示")
    
    # 运行演示
    success = demo_future_analysis()
    
    if success:
        # 显示使用示例
        show_usage_examples()
        print("\n🎉 演示成功完成！")
        print("💡 提示：运行 python test_future_windows.py 进行功能测试")
    else:
        print("\n💥 演示失败，请检查实现。")
        sys.exit(1)
