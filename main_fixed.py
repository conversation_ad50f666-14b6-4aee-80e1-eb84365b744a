#!/usr/bin/env python3
"""
A股板块数据分析程序 - 修复版本
主程序入口文件

功能：
1. 时间窗口累计涨跌幅分析
2. 板块排名频次统计
3. 单日冠军板块统计
4. 生成CSV和HTML报告

作者：AI Assistant
创建时间：2025-07-23
"""

import argparse
import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

import config


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='A股板块数据分析程序',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python main_fixed.py                           # 使用默认参数运行
  python main_fixed.py --windows 7,14,30        # 指定时间窗口
  python main_fixed.py --end-date 2025-07-22    # 指定结束日期
  python main_fixed.py --no-charts              # 不生成图表
        """
    )
    
    parser.add_argument(
        '--windows',
        type=str,
        default=','.join(map(str, config.TIME_WINDOWS)),
        help=f'时间窗口（天数），用逗号分隔 (默认: {",".join(map(str, config.TIME_WINDOWS))})'
    )
    
    parser.add_argument(
        '--end-date',
        type=str,
        default=None,
        help='分析结束日期，格式：YYYY-MM-DD (默认: 最新数据日期)'
    )
    
    parser.add_argument(
        '--output-dir',
        type=str,
        default=config.OUTPUT_DIR,
        help=f'输出目录 (默认: {config.OUTPUT_DIR})'
    )
    
    parser.add_argument(
        '--no-charts',
        action='store_true',
        help='禁用图表生成'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        default=True,  # 默认启用详细输出
        help='详细输出模式 (默认启用)'
    )
    
    parser.add_argument(
        '--top-n',
        type=int,
        default=config.TOP_N,
        help=f'排行榜显示数量 (默认: {config.TOP_N})'
    )

    parser.add_argument(
        '--export-csv',
        action='store_true',
        default=True,  # 默认启用CSV导出
        help='导出详细数据到CSV文件 (默认启用)'
    )

    parser.add_argument(
        '--quick-mode',
        action='store_true',
        help='快速模式：只分析最近的数据文件'
    )

    parser.add_argument(
        '--max-files',
        type=int,
        default=None,
        help='限制处理的最大文件数量（用于测试）'
    )

    parser.add_argument(
        '--enable-historical',
        action='store_true',
        default=True,  # 默认启用历史分析
        help='启用历史同期分析功能 (默认启用)'
    )

    return parser.parse_args()


def validate_arguments(args):
    """验证命令行参数"""
    # 验证时间窗口参数
    try:
        windows = [int(w.strip()) for w in args.windows.split(',')]
        if any(w <= 0 for w in windows):
            raise ValueError("时间窗口必须为正整数")
        args.windows = windows
    except ValueError as e:
        print(f"错误：时间窗口参数无效 - {e}")
        sys.exit(1)
    
    # 验证结束日期参数
    if args.end_date:
        try:
            datetime.strptime(args.end_date, '%Y-%m-%d')
        except ValueError:
            print("错误：结束日期格式无效，请使用 YYYY-MM-DD 格式")
            sys.exit(1)
    
    # 验证输出目录
    output_path = Path(args.output_dir)
    if not output_path.exists():
        try:
            output_path.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            print(f"错误：无法创建输出目录 {args.output_dir} - {e}")
            sys.exit(1)
    
    # 验证数据目录
    if not Path(config.DATA_DIR).exists():
        print(f"错误：数据目录 {config.DATA_DIR} 不存在")
        sys.exit(1)

    # 验证top-n参数
    if args.top_n <= 0:
        print("错误：--top-n 参数必须为正整数")
        sys.exit(1)

    # 验证max-files参数
    if args.max_files is not None and args.max_files <= 0:
        print("错误：--max-files 参数必须为正整数")
        sys.exit(1)

    return args


def main():
    """主程序入口"""
    print("=" * 60)
    print("A股板块数据分析程序 - 修复版本")
    print(f"运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 解析和验证参数
    args = parse_arguments()
    args = validate_arguments(args)
    
    if args.verbose:
        print(f"配置参数：")
        print(f"  时间窗口: {args.windows}")
        print(f"  结束日期: {args.end_date or '最新数据'}")
        print(f"  输出目录: {args.output_dir}")
        print(f"  生成图表: {not args.no_charts}")
        print(f"  排行榜数量: {args.top_n}")
        print(f"  导出CSV: {args.export_csv}")
        print(f"  快速模式: {args.quick_mode}")
        print(f"  历史分析: {args.enable_historical}")
        if args.max_files:
            print(f"  最大文件数: {args.max_files}")
        print()
    
    try:
        # 导入所有功能模块
        print("📦 导入功能模块...")
        from src.data_loader import DataLoader
        from src.time_window_analyzer import TimeWindowAnalyzer
        from src.ranking_analyzer import RankingAnalyzer
        from src.visualizer import Visualizer
        from src.report_generator import ReportGenerator
        from src.historical_analyzer import HistoricalAnalyzer
        from src.utils import setup_logging
        import pandas as pd
        from tqdm import tqdm
        import time
        print("✅ 模块导入完成")

        # 设置日志
        logger = setup_logging(args.verbose)

        # 开始执行分析
        start_time = time.time()

        print("🚀 开始执行A股板块数据分析...")
        print()

        # 1. 数据加载阶段
        print("📊 第1步：数据加载")
        print("-" * 50)

        loader = DataLoader(config.DATA_DIR)
        print(f"发现数据文件: {len(loader.file_list)} 个")

        # 处理快速模式和文件数量限制
        if args.quick_mode:
            loader.file_list = loader.file_list[-50:]
            print(f"快速模式：使用最近 {len(loader.file_list)} 个文件")
        elif args.max_files:
            loader.file_list = loader.file_list[-args.max_files:]
            print(f"限制模式：使用最近 {len(loader.file_list)} 个文件")

        # 显示数据基本信息
        date_range = loader.get_date_range()
        print(f"数据日期范围: {date_range[0]} 到 {date_range[1]}")

        # 加载数据
        print("正在加载数据...")
        data = loader.load_all_data()

        # 验证数据
        print("正在验证数据完整性...")
        is_valid = loader.validate_data(data)
        if not is_valid:
            print("❌ 数据验证失败，程序终止")
            sys.exit(1)

        # 显示数据摘要
        summary = loader.get_data_summary()
        print(f"✅ 数据加载完成")
        print(f"   总记录数: {summary['total_records']:,}")
        print(f"   板块数量: {summary['total_sectors']}")
        print(f"   内存使用: {summary['memory_usage_mb']:.1f} MB")
        print()

        # 2. 时间窗口分析阶段
        print("📈 第2步：时间窗口分析")
        print("-" * 50)

        analyzer = TimeWindowAnalyzer(data)

        # 确定分析结束日期
        if args.end_date:
            end_date = args.end_date
        else:
            end_date = analyzer.available_dates[-1]

        print(f"分析结束日期: {end_date}")
        print(f"分析时间窗口: {args.windows}")

        # 执行时间窗口分析
        window_results = {}
        for window_days in tqdm(args.windows, desc="时间窗口分析", unit="窗口"):
            performance = analyzer.calculate_window_performance(end_date, window_days)
            if not performance.empty:
                window_results[window_days] = performance
                top_sector = performance.iloc[0]
                print(f"  {window_days:2d}日窗口最佳: {top_sector['sector_name']} "
                      f"({top_sector['cumulative_return_pct']:.2f}%)")

        print(f"✅ 时间窗口分析完成，共分析 {len(window_results)} 个时间窗口")
        print()

        # 3. 排名统计分析阶段
        print("🏆 第3步：排名统计分析")
        print("-" * 50)

        ranking_analyzer = RankingAnalyzer(data)

        # 计算单日冠军统计
        print("正在统计单日冠军...")
        champions = ranking_analyzer.count_daily_champions()

        if not champions.empty:
            print(f"✅ 单日冠军统计完成，共 {len(champions)} 个板块获得过冠军")
            top_champion = champions.iloc[0]
            print(f"   最多冠军: {top_champion['sector_name']} "
                  f"({top_champion['champion_count']}次)")

        # 计算前10频次统计
        print("正在统计前10频次...")
        max_window = max(args.windows) if args.windows else 30
        ranking_frequency = ranking_analyzer.count_top10_frequency(window_days=max_window)

        if not ranking_frequency.empty:
            print(f"✅ 前10频次统计完成，共 {len(ranking_frequency)} 个板块进入过前10")
            most_frequent = ranking_frequency.iloc[0]
            print(f"   最频繁前10: {most_frequent['sector_name']} "
                  f"({most_frequent['top10_count']}次)")

        print(f"✅ 排名统计分析完成")
        print()

        # 4. 历史同期分析阶段（可选）
        historical_analysis = {}
        if args.enable_historical:
            print("📈 第4步：历史同期分析")
            print("-" * 50)

            historical_analyzer = HistoricalAnalyzer(data)
            target_date = end_date if args.end_date else analyzer.available_dates[-1]

            print(f"目标日期: {target_date}")
            print(f"分析时间窗口: {args.windows}")

            print("正在进行历史同期分析...")
            historical_analysis = historical_analyzer.calculate_historical_windows(
                target_date, args.windows
            )

            if historical_analysis and 'historical_dates' in historical_analysis:
                years_count = len(historical_analysis['historical_dates'])
                print(f"✅ 历史同期分析完成，涵盖 {years_count} 个年份")
            else:
                print("⚠️ 历史数据不足，跳过历史同期分析")

            print()
        else:
            print("⏭️ 跳过历史同期分析（使用 --enable-historical 启用）")
            print()

        # 5. 可视化生成阶段
        generated_charts = []
        if not args.no_charts:
            print("🎨 第5步：生成可视化图表")
            print("-" * 50)

            visualizer = Visualizer(args.output_dir)

            # 准备综合数据
            all_data = {
                'window_performance': window_results,
                'champions': champions,
                'ranking_frequency': ranking_frequency,
                'raw_data': data,
                'top_sectors': champions.head(6)['sector_code'].tolist() if not champions.empty else []
            }

            print("正在生成可视化图表...")
            chart_files = visualizer.generate_comprehensive_report(
                all_data,
                "A股板块数据分析报告"
            )

            generated_charts.extend(chart_files)

            if generated_charts:
                print(f"✅ 可视化图表生成完成，共生成 {len(generated_charts)} 个图表文件")
                print(f"   图表保存位置: {args.output_dir}")
            else:
                print("⚠️ 未生成任何图表文件")

            print()
        else:
            print("⏭️ 跳过图表生成（--no-charts 参数）")
            print()

        # 6. CSV导出功能
        if args.export_csv:
            print("💾 第6步：导出数据到CSV文件")
            print("-" * 50)

            csv_files = []

            # 导出时间窗口分析结果
            for window_days, performance in window_results.items():
                if not performance.empty:
                    csv_filename = f"window_performance_{window_days}d.csv"
                    csv_path = Path(args.output_dir) / csv_filename
                    performance.to_csv(csv_path, index=False, encoding='utf-8-sig')
                    csv_files.append(csv_filename)

            # 导出冠军统计结果
            if not champions.empty:
                csv_filename = "champions.csv"
                csv_path = Path(args.output_dir) / csv_filename
                champions.to_csv(csv_path, index=False, encoding='utf-8-sig')
                csv_files.append(csv_filename)

            # 导出前10频次结果
            if not ranking_frequency.empty:
                csv_filename = "ranking_frequency.csv"
                csv_path = Path(args.output_dir) / csv_filename
                ranking_frequency.to_csv(csv_path, index=False, encoding='utf-8-sig')
                csv_files.append(csv_filename)

            print(f"✅ CSV文件导出完成，共导出 {len(csv_files)} 个文件:")
            for csv_file in csv_files:
                print(f"   • {csv_file}")
            print()

        # 7. HTML报告生成
        print("📄 第7步：生成HTML综合报告")
        print("-" * 50)

        report_generator = ReportGenerator(args.output_dir)

        # 准备报告数据
        report_data = {
            'window_performance': window_results,
            'champions': champions,
            'ranking_frequency': ranking_frequency,
            'historical_analysis': historical_analysis
        }

        # 生成HTML报告
        html_file = report_generator.generate_html_report(
            report_data,
            generated_charts,
            "A股板块数据分析报告"
        )

        if html_file:
            print(f"✅ HTML报告已生成: {os.path.basename(html_file)}")
        else:
            print("⚠️ HTML报告生成失败")

        # 计算执行时间
        execution_time = time.time() - start_time

        print("\n" + "=" * 80)
        print("🎉 A股板块数据分析程序执行完成！")
        print("=" * 80)
        print(f"📊 数据概览:")
        print(f"   • 分析数据量: {summary['total_records']:,} 条记录")
        print(f"   • 板块总数: {summary['total_sectors']} 个")
        print(f"   • 交易日数: {len(analyzer.available_dates)} 天")
        print(f"   • 数据日期范围: {date_range[0]} 到 {date_range[1]}")

        print(f"\n🔍 分析结果:")
        print(f"   • 时间窗口分析: {len(window_results)} 个时间窗口")
        print(f"   • 获得冠军的板块: {len(champions)} 个")
        print(f"   • 进入前10的板块: {len(ranking_frequency)} 个")

        if generated_charts:
            print(f"   • 生成图表文件: {len(generated_charts)} 个")

        print(f"\n⏱️ 执行信息:")
        print(f"   • 总执行时间: {execution_time:.2f} 秒")
        print(f"   • 内存使用: {summary['memory_usage_mb']:.1f} MB")
        print(f"   • 输出目录: {args.output_dir}")

        print("\n" + "=" * 80)

    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 程序执行出错：{e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
