#!/usr/bin/env python3
"""
简单的未来时间窗口功能测试
"""

def test_imports():
    """测试导入"""
    print("🧪 测试导入...")
    
    try:
        import config
        print(f"✅ config导入成功")
        print(f"   TIME_WINDOWS: {config.TIME_WINDOWS}")
        print(f"   FUTURE_TIME_WINDOWS: {getattr(config, 'FUTURE_TIME_WINDOWS', 'NOT_FOUND')}")
        print(f"   DATA_YEAR_LIMIT: {getattr(config, 'DATA_YEAR_LIMIT', 'NOT_FOUND')}")
        
        from src.data_loader import DataLoader
        print(f"✅ DataLoader导入成功")
        
        from src.time_window_analyzer import TimeWindowAnalyzer
        print(f"✅ TimeWindowAnalyzer导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_analyzer_init():
    """测试分析器初始化"""
    print("\n🔧 测试分析器初始化...")
    
    try:
        from src.data_loader import DataLoader
        from src.time_window_analyzer import TimeWindowAnalyzer
        
        # 加载少量数据
        loader = DataLoader()
        loader.file_list = loader.file_list[:10]  # 只加载10个文件
        data = loader.load_all_data()
        print(f"✅ 数据加载成功: {data.shape}")
        
        # 初始化分析器
        analyzer = TimeWindowAnalyzer(data)
        print(f"✅ 分析器初始化成功")
        print(f"   全部数据日期范围: {analyzer.date_range}")
        print(f"   过滤数据日期范围: {analyzer.filtered_date_range}")
        print(f"   全部交易日数: {len(analyzer.available_dates)}")
        print(f"   过滤交易日数: {len(analyzer.filtered_available_dates)}")
        
        # 检查新方法是否存在
        methods_to_check = [
            'calculate_future_window_performance',
            'calculate_multiple_future_windows',
            'calculate_combined_windows',
            '_get_future_trading_days_in_window',
            '_filter_data_for_future_analysis'
        ]
        
        for method_name in methods_to_check:
            if hasattr(analyzer, method_name):
                print(f"✅ 方法 {method_name} 存在")
            else:
                print(f"❌ 方法 {method_name} 不存在")
        
        return analyzer
        
    except Exception as e:
        print(f"❌ 分析器初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_future_analysis(analyzer):
    """测试未来分析功能"""
    print("\n🔮 测试未来分析功能...")
    
    try:
        if not analyzer:
            print("❌ 分析器为空，跳过测试")
            return False
            
        # 选择一个测试日期
        if not analyzer.filtered_available_dates:
            print("❌ 没有过滤后的数据，跳过测试")
            return False
            
        test_date = analyzer.filtered_available_dates[-50]  # 选择一个较早的日期
        print(f"测试日期: {test_date}")
        
        # 测试未来30日窗口
        print("测试未来30日窗口...")
        future_result = analyzer.calculate_future_window_performance(test_date, 30)
        
        if not future_result.empty:
            print(f"✅ 未来30日分析成功，分析了 {len(future_result)} 个板块")
            top_3 = future_result.head(3)
            print("前3名板块:")
            for i, (_, row) in enumerate(top_3.iterrows(), 1):
                print(f"   {i}. {row['sector_name']}: {row['cumulative_return_pct']:.2f}%")
        else:
            print("⚠️ 未来30日分析结果为空")
            
        # 测试组合分析
        print("\n测试组合分析...")
        combined_result = analyzer.calculate_combined_windows(
            test_date, 
            historical_windows=[7, 14], 
            future_windows=[30, 60]
        )
        
        hist_count = sum(len(df) for df in combined_result['historical'].values() if not df.empty)
        future_count = sum(len(df) for df in combined_result['future'].values() if not df.empty)
        
        print(f"✅ 组合分析成功")
        print(f"   历史分析记录数: {hist_count}")
        print(f"   未来分析记录数: {future_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 未来分析测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始简单未来时间窗口功能测试")
    print("=" * 60)
    
    # 测试导入
    if not test_imports():
        print("\n💥 导入测试失败")
        return False
    
    # 测试分析器初始化
    analyzer = test_analyzer_init()
    if not analyzer:
        print("\n💥 分析器初始化失败")
        return False
    
    # 测试未来分析功能
    if not test_future_analysis(analyzer):
        print("\n💥 未来分析功能测试失败")
        return False
    
    print("\n🎉 所有测试通过！未来时间窗口分析功能正常工作。")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
