#!/usr/bin/env python3
"""
测试参数解析
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_args():
    """测试参数解析"""
    print("🧪 测试参数解析")
    print("=" * 50)
    
    try:
        # 模拟命令行参数
        sys.argv = ['test_args.py', '--enable-future-analysis', '--verbose']
        
        # 导入main模块的参数解析函数
        from main import parse_arguments, validate_arguments
        
        # 解析参数
        args = parse_arguments()
        print(f"原始参数解析结果:")
        print(f"  enable_future_analysis: {getattr(args, 'enable_future_analysis', 'NOT_FOUND')}")
        print(f"  future_windows: {getattr(args, 'future_windows', 'NOT_FOUND')}")
        print(f"  windows: {getattr(args, 'windows', 'NOT_FOUND')}")
        print(f"  verbose: {getattr(args, 'verbose', 'NOT_FOUND')}")
        
        # 验证参数
        args = validate_arguments(args)
        print(f"\n验证后参数:")
        print(f"  enable_future_analysis: {getattr(args, 'enable_future_analysis', 'NOT_FOUND')}")
        print(f"  future_windows: {getattr(args, 'future_windows', 'NOT_FOUND')}")
        print(f"  windows: {getattr(args, 'windows', 'NOT_FOUND')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 参数解析测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_config():
    """测试配置"""
    print("\n🔧 测试配置")
    print("=" * 50)
    
    try:
        import config
        
        print(f"TIME_WINDOWS: {config.TIME_WINDOWS}")
        print(f"FUTURE_TIME_WINDOWS: {getattr(config, 'FUTURE_TIME_WINDOWS', 'NOT_FOUND')}")
        print(f"DATA_YEAR_LIMIT: {getattr(config, 'DATA_YEAR_LIMIT', 'NOT_FOUND')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试")
    
    config_ok = test_config()
    args_ok = test_args()
    
    if config_ok and args_ok:
        print("\n✅ 所有测试通过")
    else:
        print("\n❌ 测试失败")
        sys.exit(1)
