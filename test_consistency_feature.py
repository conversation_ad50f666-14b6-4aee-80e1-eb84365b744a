#!/usr/bin/env python3
"""
测试一致性表现板块功能
"""

import sys
import os
from datetime import datetime
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_loader import DataLoader
from src.time_window_analyzer import TimeWindowAnalyzer
from src.ranking_analyzer import RankingAnalyzer
from src.report_generator import ReportGenerator
from src.historical_analyzer import HistoricalAnalyzer

def test_consistency_analysis():
    """测试一致性分析功能"""
    print("🧪 测试一致性分析功能...")
    
    # 加载少量数据进行测试
    data_loader = DataLoader('data')
    data_loader.max_files = 100  # 只加载100个文件进行测试
    
    print("   正在加载测试数据...")
    data = data_loader.load_all_data()
    
    if data.empty:
        print("   ❌ 无法加载测试数据")
        return False
    
    print(f"   测试数据加载完成: {len(data)} 条记录")
    
    # 测试历史分析器
    historical_analyzer = HistoricalAnalyzer(data)
    
    # 获取最新日期
    latest_date = data.index.get_level_values('date').max()
    print(f"   最新日期: {latest_date}")
    
    # 进行历史分析
    print("   正在进行历史分析...")
    historical_results = historical_analyzer.calculate_historical_windows(
        latest_date, [7, 14]
    )
    
    # 检查一致性分析数据
    consistency_analysis = historical_results.get('consistency_analysis', {})
    
    if consistency_analysis:
        print("   ✅ 一致性分析数据生成成功")
        for window_days, analysis_data in consistency_analysis.items():
            consistent_performers = analysis_data.get('consistent_performers', [])
            total_years = analysis_data.get('total_years_analyzed', 0)
            print(f"     {window_days}日窗口: {len(consistent_performers)} 个一致性板块, 分析了 {total_years} 年数据")
            
            # 显示前3个一致性板块
            for i, performer in enumerate(consistent_performers[:3]):
                print(f"       {i+1}. {performer['sector_name']} ({performer['sector_code']}) - {performer['consistency_percentage']}%")
        
        return True
    else:
        print("   ❌ 一致性分析数据生成失败")
        return False

def test_consistency_html_generation():
    """测试一致性HTML生成功能"""
    print("🧪 测试一致性HTML生成功能...")
    
    # 创建测试数据
    test_consistency_data = {
        7: {
            'consistent_performers': [
                {
                    'sector_code': 'BK0726',
                    'sector_name': '工程咨询服务',
                    'consistency_rate': 0.667,
                    'appearances': 4,
                    'total_years': 6,
                    'appearing_years': [2025, 2024, 2023, 2021],
                    'consistency_percentage': 66.7
                },
                {
                    'sector_code': 'BK0447',
                    'sector_name': '互联网服务',
                    'consistency_rate': 0.5,
                    'appearances': 3,
                    'total_years': 6,
                    'appearing_years': [2025, 2023, 2022],
                    'consistency_percentage': 50.0
                }
            ],
            'total_sectors_analyzed': 50,
            'total_years_analyzed': 6,
            'consistency_threshold': 50.0,
            'summary': {
                'qualified_sectors': 2,
                'analysis_period': '2020-2025'
            }
        }
    }
    
    report_generator = ReportGenerator('output')
    
    # 测试一致性板块HTML生成
    consistency_html = report_generator._generate_consistency_section(
        test_consistency_data[7], 7
    )
    
    # 检查是否包含必要的HTML元素
    required_elements = [
        'consistency-section',
        'consistency-title',
        'consistency-cards',
        'consistency-card',
        'BK0726',
        '工程咨询服务',
        '66.7%',
        '4/6年表现优异'
    ]
    
    missing_elements = []
    for element in required_elements:
        if element not in consistency_html:
            missing_elements.append(element)
    
    if not missing_elements:
        print("   ✅ 一致性HTML生成成功")
        print(f"   HTML长度: {len(consistency_html)} 字符")
        return True
    else:
        print("   ❌ 一致性HTML生成失败")
        print(f"   缺少元素: {missing_elements}")
        return False

def test_full_integration():
    """测试完整集成功能"""
    print("🧪 测试完整集成功能...")
    
    try:
        # 加载少量数据
        data_loader = DataLoader('data')
        data_loader.max_files = 50
        
        print("   正在加载数据...")
        data = data_loader.load_all_data()
        
        if data.empty:
            print("   ❌ 无法加载数据")
            return False
        
        # 进行时间窗口分析
        print("   正在进行时间窗口分析...")
        time_analyzer = TimeWindowAnalyzer(data)
        latest_date = data.index.get_level_values('date').max()
        window_results = {}
        
        for window_days in [7, 14]:
            result = time_analyzer.analyze_window_performance(latest_date, window_days)
            if not result.empty:
                window_results[window_days] = result
        
        # 进行历史分析
        print("   正在进行历史分析...")
        historical_analyzer = HistoricalAnalyzer(data)
        historical_results = historical_analyzer.calculate_historical_windows(
            latest_date, [7, 14]
        )
        
        # 生成HTML报告
        print("   正在生成HTML报告...")
        report_generator = ReportGenerator('output')
        
        report_data = {
            'window_performance': window_results,
            'champions': data.groupby('sector_code').first().head(10),  # 简化的冠军数据
            'ranking_frequency': data.groupby('sector_code').first().head(10),  # 简化的频次数据
            'historical_analysis': historical_results
        }
        
        html_file = report_generator.generate_html_report(
            report_data,
            [],
            "一致性功能测试报告"
        )
        
        if html_file and os.path.exists(html_file):
            print(f"   ✅ HTML报告生成成功: {os.path.basename(html_file)}")
            
            # 检查HTML文件是否包含一致性相关内容
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            consistency_keywords = [
                'consistency-section',
                '一致性表现板块',
                'consistency-percentage',
                '年表现优异'
            ]
            
            found_keywords = [kw for kw in consistency_keywords if kw in html_content]
            
            if len(found_keywords) >= 3:
                print(f"   ✅ HTML包含一致性功能: 找到 {len(found_keywords)}/{len(consistency_keywords)} 个关键词")
                return True
            else:
                print(f"   ⚠️ HTML可能缺少一致性功能: 只找到 {len(found_keywords)}/{len(consistency_keywords)} 个关键词")
                return False
        else:
            print("   ❌ HTML报告生成失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 集成测试出错: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 一致性表现板块功能测试")
    print("=" * 60)
    
    results = []
    
    # 测试1: 一致性分析功能
    results.append(test_consistency_analysis())
    print()
    
    # 测试2: 一致性HTML生成功能
    results.append(test_consistency_html_generation())
    print()
    
    # 测试3: 完整集成功能
    results.append(test_full_integration())
    print()
    
    # 总结
    print("=" * 60)
    print("🧪 测试结果总结")
    print("=" * 60)
    
    test_names = [
        "一致性分析功能",
        "一致性HTML生成功能", 
        "完整集成功能"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 一致性表现板块功能测试全部通过！")
    else:
        print("⚠️ 部分功能需要进一步调试")

if __name__ == '__main__':
    main()
