#!/usr/bin/env python3
"""
简单测试一致性功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.report_generator import ReportGenerator

def test_consistency_html():
    """测试一致性HTML生成"""
    print("🧪 测试一致性HTML生成...")
    
    # 创建测试数据
    test_consistency_data = {
        'consistent_performers': [
            {
                'sector_code': 'BK0726',
                'sector_name': '工程咨询服务',
                'consistency_rate': 0.667,
                'appearances': 4,
                'total_years': 6,
                'appearing_years': [2025, 2024, 2023, 2021],
                'consistency_percentage': 66.7
            },
            {
                'sector_code': 'BK0447',
                'sector_name': '互联网服务',
                'consistency_rate': 0.5,
                'appearances': 3,
                'total_years': 6,
                'appearing_years': [2025, 2023, 2022],
                'consistency_percentage': 50.0
            },
            {
                'sector_code': 'BK0424',
                'sector_name': '水泥建材',
                'consistency_rate': 0.5,
                'appearances': 3,
                'total_years': 6,
                'appearing_years': [2025, 2024, 2020],
                'consistency_percentage': 50.0
            }
        ],
        'total_sectors_analyzed': 50,
        'total_years_analyzed': 6,
        'consistency_threshold': 50.0,
        'summary': {
            'qualified_sectors': 3,
            'analysis_period': '2020-2025'
        }
    }
    
    report_generator = ReportGenerator('output')
    
    # 测试一致性板块HTML生成
    consistency_html = report_generator._generate_consistency_section(
        test_consistency_data, 7
    )
    
    print(f"生成的HTML长度: {len(consistency_html)} 字符")
    
    # 检查关键元素
    required_elements = [
        'consistency-section',
        'consistency-title',
        'consistency-cards',
        'consistency-card',
        'BK0726',
        '工程咨询服务',
        '66.7%',
        '4/6年表现优异',
        '2025, 2024, 2023, 2021'
    ]
    
    missing_elements = []
    for element in required_elements:
        if element not in consistency_html:
            missing_elements.append(element)
    
    if not missing_elements:
        print("✅ 一致性HTML生成成功")
        print("包含的关键元素:")
        for element in required_elements:
            print(f"  ✓ {element}")
        
        # 保存HTML到文件以便查看
        with open('output/test_consistency.html', 'w', encoding='utf-8') as f:
            f.write(f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>一致性功能测试</title>
    <style>
        /* 一致性表现板块样式 */
        .consistency-section {{
            background-color: #f8f9fa;
            border: 2px solid #28a745;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
        }}
        .consistency-title {{
            color: #28a745;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }}
        .consistency-title::before {{
            content: "🏆";
            margin-right: 8px;
            font-size: 20px;
        }}
        .consistency-cards {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }}
        .consistency-card {{
            background-color: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }}
        .consistency-card:hover {{
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }}
        .consistency-card-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }}
        .consistency-sector-name {{
            font-weight: bold;
            color: #2c3e50;
            font-size: 16px;
        }}
        .consistency-percentage {{
            background-color: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }}
        .consistency-sector-code {{
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 5px;
        }}
        .consistency-stats {{
            color: #495057;
            font-size: 14px;
        }}
    </style>
</head>
<body>
    <h1>一致性表现板块功能测试</h1>
    {consistency_html}
</body>
</html>
            """)
        
        print("✅ 测试HTML文件已保存: output/test_consistency.html")
        return True
    else:
        print("❌ 一致性HTML生成失败")
        print(f"缺少元素: {missing_elements}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 一致性功能简单测试")
    print("=" * 60)
    
    result = test_consistency_html()
    
    print("\n" + "=" * 60)
    if result:
        print("🎉 一致性功能测试通过！")
    else:
        print("⚠️ 一致性功能需要调试")

if __name__ == '__main__':
    main()
