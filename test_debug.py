#!/usr/bin/env python3
"""
简单的调试测试程序
"""

import sys
import os
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """测试模块导入"""
    try:
        print("测试模块导入...")
        from src.data_loader import DataLoader
        from src.time_window_analyzer import TimeWindowAnalyzer
        from src.ranking_analyzer import RankingAnalyzer
        print("✅ 所有模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_data_loading():
    """测试数据加载"""
    try:
        print("测试数据加载...")
        from src.data_loader import DataLoader
        import config
        
        loader = DataLoader(config.DATA_DIR)
        print(f"发现数据文件: {len(loader.file_list)} 个")
        
        # 只加载最近的3个文件进行测试
        loader.file_list = loader.file_list[-3:]
        print(f"测试模式：使用最近 {len(loader.file_list)} 个文件")
        
        data = loader.load_all_data()
        print(f"✅ 数据加载成功，形状: {data.shape}")
        return data
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_time_window_analysis(data):
    """测试时间窗口分析"""
    try:
        print("测试时间窗口分析...")
        from src.time_window_analyzer import TimeWindowAnalyzer
        
        analyzer = TimeWindowAnalyzer(data)
        end_date = analyzer.available_dates[-1]
        print(f"分析结束日期: {end_date}")
        
        # 只测试一个时间窗口
        performance = analyzer.calculate_window_performance(end_date, 7)
        print(f"✅ 时间窗口分析成功，结果数量: {len(performance)}")
        return performance
    except Exception as e:
        print(f"❌ 时间窗口分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_ranking_analysis(data):
    """测试排名分析"""
    try:
        print("测试排名分析...")
        from src.ranking_analyzer import RankingAnalyzer
        
        ranking_analyzer = RankingAnalyzer(data)
        champions = ranking_analyzer.count_daily_champions()
        print(f"✅ 排名分析成功，冠军数量: {len(champions)}")
        return champions
    except Exception as e:
        print(f"❌ 排名分析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主测试函数"""
    print("=" * 60)
    print("调试测试程序")
    print(f"当前时间: {datetime.now()}")
    print("=" * 60)
    
    # 测试模块导入
    if not test_imports():
        return
    
    # 测试数据加载
    data = test_data_loading()
    if data is None:
        return
    
    # 测试时间窗口分析
    window_results = test_time_window_analysis(data)
    if window_results is None:
        return
    
    # 测试排名分析
    ranking_results = test_ranking_analysis(data)
    if ranking_results is None:
        return
    
    print("=" * 60)
    print("🎉 所有测试通过！")
    print("=" * 60)

if __name__ == '__main__':
    main()
