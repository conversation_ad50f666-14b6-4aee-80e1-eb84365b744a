#!/usr/bin/env python3
"""
测试未来时间窗口分析功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_future_windows():
    """测试未来时间窗口分析功能"""
    print("🧪 测试未来时间窗口分析功能")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from src.data_loader import DataLoader
        from src.time_window_analyzer import TimeWindowAnalyzer
        import config
        import pandas as pd
        
        # 1. 加载测试数据
        print("\n1. 加载测试数据...")
        loader = DataLoader()
        # 只加载前30个文件进行测试
        loader.file_list = loader.file_list[:30]
        data = loader.load_all_data()
        print(f"   ✓ 数据加载完成，形状: {data.shape}")
        
        # 2. 初始化分析器
        print("\n2. 初始化时间窗口分析器...")
        analyzer = TimeWindowAnalyzer(data)
        print(f"   ✓ 分析器初始化完成")
        print(f"   ✓ 全部数据日期范围: {analyzer.date_range[0]} 到 {analyzer.date_range[1]}")
        print(f"   ✓ 未来分析数据日期范围: {analyzer.filtered_date_range[0]} 到 {analyzer.filtered_date_range[1]}")
        print(f"   ✓ 全部交易日数: {len(analyzer.available_dates)}")
        print(f"   ✓ 未来分析交易日数: {len(analyzer.filtered_available_dates)}")
        
        # 3. 测试历史时间窗口分析
        print("\n3. 测试历史时间窗口分析...")
        reference_date = analyzer.available_dates[-100]  # 选择一个较早的日期作为参考
        print(f"   参考日期: {reference_date}")
        
        historical_windows = [7, 14, 30]
        for window_days in historical_windows:
            performance = analyzer.calculate_window_performance(reference_date, window_days)
            if not performance.empty:
                top_sector = performance.iloc[0]
                print(f"   历史{window_days:2d}日窗口最佳: {top_sector['sector_name']} "
                      f"({top_sector['cumulative_return_pct']:.2f}%)")
            else:
                print(f"   历史{window_days:2d}日窗口: 无数据")
        
        # 4. 测试未来时间窗口分析
        print("\n4. 测试未来时间窗口分析...")
        future_windows = config.FUTURE_TIME_WINDOWS
        print(f"   未来时间窗口配置: {future_windows}")
        
        for window_days in future_windows:
            performance = analyzer.calculate_future_window_performance(reference_date, window_days)
            if not performance.empty:
                top_sector = performance.iloc[0]
                print(f"   未来{window_days:2d}日窗口最佳: {top_sector['sector_name']} "
                      f"({top_sector['cumulative_return_pct']:.2f}%)")
                print(f"     时间范围: {top_sector['window_start_date'].strftime('%Y-%m-%d')} 到 "
                      f"{top_sector['window_end_date'].strftime('%Y-%m-%d')}")
            else:
                print(f"   未来{window_days:2d}日窗口: 无数据")
        
        # 5. 测试组合分析
        print("\n5. 测试组合分析...")
        combined_results = analyzer.calculate_combined_windows(
            reference_date, 
            historical_windows=[7, 14], 
            future_windows=[30, 60]
        )
        
        print(f"   ✓ 历史分析结果: {len(combined_results['historical'])} 个时间窗口")
        print(f"   ✓ 未来分析结果: {len(combined_results['future'])} 个时间窗口")
        
        # 6. 验证数据年份限制
        print("\n6. 验证数据年份限制...")
        max_year = config.DATA_YEAR_LIMIT['future_analysis_max_year']
        print(f"   未来分析最大年份限制: {max_year}")
        
        # 检查过滤后的数据是否符合年份限制
        filtered_dates = analyzer.filtered_data_for_future.index.get_level_values('date')
        if not filtered_dates.empty:
            actual_max_year = filtered_dates.max().year
            actual_min_year = filtered_dates.min().year
            print(f"   实际数据年份范围: {actual_min_year} - {actual_max_year}")
            
            if actual_max_year <= max_year:
                print(f"   ✅ 年份限制验证通过")
            else:
                print(f"   ❌ 年份限制验证失败: 实际最大年份 {actual_max_year} > 限制 {max_year}")
        else:
            print(f"   ⚠️ 过滤后的数据为空")
        
        # 7. 性能测试
        print("\n7. 性能测试...")
        import time
        
        start_time = time.time()
        test_performance = analyzer.calculate_future_window_performance(reference_date, 30)
        end_time = time.time()
        
        print(f"   未来30日窗口分析耗时: {end_time - start_time:.3f}秒")
        print(f"   分析板块数量: {len(test_performance) if not test_performance.empty else 0}")
        
        print("\n✅ 所有测试完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_config():
    """测试配置文件更新"""
    print("\n🔧 测试配置文件...")
    
    try:
        import config
        
        print(f"   历史时间窗口: {config.TIME_WINDOWS}")
        print(f"   未来时间窗口: {config.FUTURE_TIME_WINDOWS}")
        print(f"   数据年份限制: {config.DATA_YEAR_LIMIT}")
        
        # 验证配置的合理性
        assert isinstance(config.FUTURE_TIME_WINDOWS, list), "FUTURE_TIME_WINDOWS应该是列表"
        assert all(isinstance(w, int) and w > 0 for w in config.FUTURE_TIME_WINDOWS), "未来时间窗口应该是正整数"
        assert isinstance(config.DATA_YEAR_LIMIT, dict), "DATA_YEAR_LIMIT应该是字典"
        assert 'future_analysis_max_year' in config.DATA_YEAR_LIMIT, "缺少future_analysis_max_year配置"
        
        print("   ✅ 配置文件验证通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 配置文件测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试未来时间窗口分析功能")
    print("=" * 60)
    
    # 测试配置
    config_ok = test_config()
    
    if config_ok:
        # 测试功能
        test_ok = test_future_windows()
        
        if test_ok:
            print("\n🎉 所有测试通过！未来时间窗口分析功能已成功实现。")
        else:
            print("\n💥 功能测试失败，请检查实现。")
            sys.exit(1)
    else:
        print("\n💥 配置测试失败，请检查配置文件。")
        sys.exit(1)
