#!/usr/bin/env python3
"""
测试三项修改的脚本
1. 删除趋势分析部分
2. 展示所有符合一致性条件的板块
3. 修正涨跌颜色
"""

import sys
import os
import re

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.report_generator import ReportGenerator

def test_trend_analysis_removal():
    """测试趋势分析部分是否已删除"""
    print("🧪 测试趋势分析部分删除...")
    
    # 读取report_generator.py文件
    with open('src/report_generator.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否还包含趋势分析相关代码
    trend_keywords = [
        '趋势分析',
        'trend_analysis',
        '平均收益率趋势',
        'avg_return_trend',
        '一致性表现板块</h5>',
        'consistent_performers[:5]'
    ]
    
    found_keywords = []
    for keyword in trend_keywords:
        if keyword in content:
            found_keywords.append(keyword)
    
    if not found_keywords:
        print("   ✅ 趋势分析部分已成功删除")
        return True
    else:
        print("   ❌ 趋势分析部分删除不完整")
        print(f"   仍包含关键词: {found_keywords}")
        return False

def test_consistency_display_all():
    """测试一致性板块是否显示所有符合条件的板块"""
    print("🧪 测试一致性板块显示所有符合条件的板块...")
    
    # 创建测试数据 - 包含5个符合条件的板块
    test_consistency_data = {
        'consistent_performers': [
            {
                'sector_code': 'BK0726',
                'sector_name': '工程咨询服务',
                'consistency_rate': 0.667,
                'appearances': 4,
                'total_years': 6,
                'appearing_years': [2025, 2024, 2023, 2021],
                'consistency_percentage': 66.7
            },
            {
                'sector_code': 'BK0447',
                'sector_name': '互联网服务',
                'consistency_rate': 0.6,
                'appearances': 3,
                'total_years': 5,
                'appearing_years': [2025, 2023, 2022],
                'consistency_percentage': 60.0
            },
            {
                'sector_code': 'BK0424',
                'sector_name': '水泥建材',
                'consistency_rate': 0.55,
                'appearances': 3,
                'total_years': 6,
                'appearing_years': [2025, 2024, 2020],
                'consistency_percentage': 55.0
            },
            {
                'sector_code': 'BK0479',
                'sector_name': '钢铁行业',
                'consistency_rate': 0.5,
                'appearances': 3,
                'total_years': 6,
                'appearing_years': [2025, 2023, 2021],
                'consistency_percentage': 50.0
            },
            {
                'sector_code': 'BK0437',
                'sector_name': '煤炭行业',
                'consistency_rate': 0.5,
                'appearances': 2,
                'total_years': 4,
                'appearing_years': [2025, 2022],
                'consistency_percentage': 50.0
            }
        ],
        'total_sectors_analyzed': 50,
        'total_years_analyzed': 6,
        'consistency_threshold': 50.0,
        'summary': {
            'qualified_sectors': 5,
            'analysis_period': '2020-2025'
        }
    }
    
    report_generator = ReportGenerator('output')
    
    # 生成一致性板块HTML
    consistency_html = report_generator._generate_consistency_section(
        test_consistency_data, 7
    )
    
    # 检查是否包含所有5个板块
    all_sectors_present = all([
        'BK0726' in consistency_html,
        'BK0447' in consistency_html,
        'BK0424' in consistency_html,
        'BK0479' in consistency_html,
        'BK0437' in consistency_html,
        '工程咨询服务' in consistency_html,
        '互联网服务' in consistency_html,
        '水泥建材' in consistency_html,
        '钢铁行业' in consistency_html,
        '煤炭行业' in consistency_html
    ])
    
    # 检查是否不包含"还有更多板块"的提示
    no_more_hint = '还有' not in consistency_html and '个板块符合一致性条件' not in consistency_html
    
    if all_sectors_present and no_more_hint:
        print("   ✅ 一致性板块显示所有符合条件的板块")
        print(f"   包含 {len(test_consistency_data['consistent_performers'])} 个板块")
        return True
    else:
        print("   ❌ 一致性板块显示不完整")
        if not all_sectors_present:
            print("   缺少部分板块")
        if not no_more_hint:
            print("   仍包含'还有更多板块'提示")
        return False

def test_color_scheme():
    """测试涨跌颜色是否符合中国股市习惯"""
    print("🧪 测试涨跌颜色修正...")
    
    # 读取report_generator.py文件
    with open('src/report_generator.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查CSS颜色定义
    positive_color_correct = '.positive { color: #e74c3c;' in content  # 上涨红色
    negative_color_correct = '.negative { color: #27ae60;' in content  # 下跌绿色
    
    # 检查注释是否存在
    china_habit_comment = '中国股市习惯' in content
    
    if positive_color_correct and negative_color_correct and china_habit_comment:
        print("   ✅ 涨跌颜色已修正为中国股市习惯")
        print("   上涨: 红色 (#e74c3c)")
        print("   下跌: 绿色 (#27ae60)")
        return True
    else:
        print("   ❌ 涨跌颜色修正不完整")
        if not positive_color_correct:
            print("   上涨颜色未正确设置为红色")
        if not negative_color_correct:
            print("   下跌颜色未正确设置为绿色")
        if not china_habit_comment:
            print("   缺少中国股市习惯注释")
        return False

def test_html_generation():
    """测试HTML生成是否正常"""
    print("🧪 测试HTML生成功能...")
    
    try:
        report_generator = ReportGenerator('output')
        
        # 创建简单的测试数据
        test_data = {
            'window_performance': {},
            'champions': None,
            'ranking_frequency': None,
            'historical_analysis': {
                'multi_year_rankings': {
                    7: {
                        2025: {
                            'data': [
                                {
                                    'rank': 1,
                                    'sector_code': 'BK0726',
                                    'sector_name': '工程咨询服务',
                                    'cumulative_return_pct': 10.5,
                                    'avg_daily_change_pct': 1.2,
                                    'volatility': 2.1,
                                    'trading_days': 7
                                }
                            ],
                            'end_date': '2025-07-22',
                            'total_sectors': 1
                        }
                    }
                },
                'consistency_analysis': {
                    7: {
                        'consistent_performers': [
                            {
                                'sector_code': 'BK0726',
                                'sector_name': '工程咨询服务',
                                'consistency_rate': 0.667,
                                'appearances': 4,
                                'total_years': 6,
                                'appearing_years': [2025, 2024, 2023, 2021],
                                'consistency_percentage': 66.7
                            }
                        ],
                        'total_sectors_analyzed': 50,
                        'total_years_analyzed': 6,
                        'consistency_threshold': 50.0,
                        'summary': {
                            'qualified_sectors': 1,
                            'analysis_period': '2020-2025'
                        }
                    }
                }
            }
        }
        
        # 生成HTML内容
        html_content = report_generator._generate_report_content(test_data)
        
        # 检查关键元素
        has_consistency_section = 'consistency-section' in html_content
        has_window_tabs = 'window-tabs' in html_content
        has_year_tabs = 'year-tabs' in html_content
        no_trend_analysis = '趋势分析' not in html_content
        
        if has_consistency_section and has_window_tabs and has_year_tabs and no_trend_analysis:
            print("   ✅ HTML生成功能正常")
            return True
        else:
            print("   ❌ HTML生成功能异常")
            return False
            
    except Exception as e:
        print(f"   ❌ HTML生成出错: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 股票分析报告修改验证测试")
    print("=" * 60)
    
    results = []
    
    # 测试1: 趋势分析部分删除
    results.append(test_trend_analysis_removal())
    print()
    
    # 测试2: 一致性板块显示所有符合条件的板块
    results.append(test_consistency_display_all())
    print()
    
    # 测试3: 涨跌颜色修正
    results.append(test_color_scheme())
    print()
    
    # 测试4: HTML生成功能
    results.append(test_html_generation())
    print()
    
    # 总结
    print("=" * 60)
    print("🧪 测试结果总结")
    print("=" * 60)
    
    test_names = [
        "趋势分析部分删除",
        "一致性板块显示所有符合条件的板块", 
        "涨跌颜色修正",
        "HTML生成功能"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有修改验证测试通过！")
    else:
        print("⚠️ 部分修改需要进一步调试")

if __name__ == '__main__':
    main()
