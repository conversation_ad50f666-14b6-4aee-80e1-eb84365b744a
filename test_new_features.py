#!/usr/bin/env python3
"""
测试新功能的脚本
验证图表功能是否被禁用，多年份标签页功能是否正常
"""

import sys
import os
from datetime import datetime
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_loader import DataLoader
from src.time_window_analyzer import TimeWindowAnalyzer
from src.ranking_analyzer import RankingAnalyzer
from src.report_generator import ReportGenerator
from src.historical_analyzer import HistoricalAnalyzer
from src.visualizer import Visualizer

def test_visualizer():
    """测试可视化器是否被禁用"""
    print("🧪 测试可视化器功能...")
    
    visualizer = Visualizer('output')
    
    # 测试图表生成方法
    import pandas as pd
    test_data = pd.DataFrame({
        'sector_name': ['测试板块1', '测试板块2'],
        'sector_code': ['TEST01', 'TEST02'],
        'cumulative_return_pct': [10.5, 8.3],
        'avg_daily_change_pct': [1.2, 0.9],
        'volatility': [2.1, 1.8],
        'trading_days': [7, 7]
    })
    
    result = visualizer.plot_window_performance(test_data, 7)
    print(f"   图表生成结果: {result}")
    
    if result == "":
        print("   ✅ 可视化器已成功禁用")
    else:
        print("   ❌ 可视化器仍在生成图表")
    
    return result == ""

def test_historical_analyzer():
    """测试历史分析器的多年份功能"""
    print("🧪 测试历史分析器多年份功能...")
    
    # 加载少量数据进行测试
    data_loader = DataLoader('data')
    data_loader.max_files = 50  # 只加载50个文件进行测试
    
    print("   正在加载测试数据...")
    data = data_loader.load_all_data()
    
    if data.empty:
        print("   ❌ 无法加载测试数据")
        return False
    
    print(f"   测试数据加载完成: {len(data)} 条记录")
    
    # 测试历史分析器
    historical_analyzer = HistoricalAnalyzer(data)
    
    # 获取最新日期
    latest_date = data.index.get_level_values('date').max()
    print(f"   最新日期: {latest_date}")
    
    # 进行历史分析
    print("   正在进行历史分析...")
    historical_results = historical_analyzer.calculate_historical_windows(
        latest_date, [7, 14]
    )
    
    # 检查多年份排行榜数据
    multi_year_rankings = historical_results.get('multi_year_rankings', {})
    
    if multi_year_rankings:
        print("   ✅ 多年份排行榜数据生成成功")
        for window_days, year_data in multi_year_rankings.items():
            print(f"     {window_days}日窗口: {len(year_data)} 个年份")
            for year, data_info in year_data.items():
                data_count = len(data_info.get('data', []))
                print(f"       {year}年: {data_count} 条数据")
        return True
    else:
        print("   ❌ 多年份排行榜数据生成失败")
        return False

def test_report_generator():
    """测试报告生成器的标签页功能"""
    print("🧪 测试报告生成器标签页功能...")
    
    # 创建测试数据
    test_historical_analysis = {
        'multi_year_rankings': {
            7: {
                2025: {
                    'data': [
                        {
                            'rank': 1,
                            'sector_code': 'TEST01',
                            'sector_name': '测试板块1',
                            'cumulative_return_pct': 10.5,
                            'avg_daily_change_pct': 1.2,
                            'volatility': 2.1,
                            'trading_days': 7
                        }
                    ],
                    'end_date': '2025-07-22',
                    'total_sectors': 1
                },
                2024: {
                    'data': [
                        {
                            'rank': 1,
                            'sector_code': 'TEST02',
                            'sector_name': '测试板块2',
                            'cumulative_return_pct': 8.3,
                            'avg_daily_change_pct': 0.9,
                            'volatility': 1.8,
                            'trading_days': 7
                        }
                    ],
                    'end_date': '2024-07-22',
                    'total_sectors': 1
                }
            }
        }
    }
    
    report_generator = ReportGenerator('output')
    
    # 测试多年份标签页生成
    tabs_html = report_generator._generate_multi_year_tabs(
        test_historical_analysis['multi_year_rankings']
    )
    
    # 检查是否包含标签页相关的HTML元素
    has_tabs = all([
        'window-tabs' in tabs_html,
        'year-tabs' in tabs_html,
        'showWindowTab' in tabs_html,
        'showYearTab' in tabs_html,
        '2025年' in tabs_html,
        '2024年' in tabs_html
    ])
    
    if has_tabs:
        print("   ✅ 多年份标签页HTML生成成功")
        print(f"   HTML长度: {len(tabs_html)} 字符")
        return True
    else:
        print("   ❌ 多年份标签页HTML生成失败")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 新功能测试程序")
    print("=" * 60)
    
    results = []
    
    # 测试1: 可视化器禁用
    results.append(test_visualizer())
    print()
    
    # 测试2: 历史分析器多年份功能
    results.append(test_historical_analyzer())
    print()
    
    # 测试3: 报告生成器标签页功能
    results.append(test_report_generator())
    print()
    
    # 总结
    print("=" * 60)
    print("🧪 测试结果总结")
    print("=" * 60)
    
    test_names = [
        "可视化器禁用功能",
        "历史分析器多年份功能", 
        "报告生成器标签页功能"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    success_count = sum(results)
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有新功能测试通过！")
    else:
        print("⚠️ 部分功能需要进一步调试")

if __name__ == '__main__':
    main()
