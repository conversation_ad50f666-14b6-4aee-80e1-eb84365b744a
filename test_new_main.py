#!/usr/bin/env python3
"""
直接测试新的main.py功能
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_main():
    """测试新的main.py功能"""
    print("🧪 测试新的main.py功能")
    print("=" * 60)
    
    try:
        # 清除可能的模块缓存
        if 'main' in sys.modules:
            del sys.modules['main']
        if 'config' in sys.modules:
            del sys.modules['config']
            
        # 重新导入
        import config
        print(f"✅ 配置导入成功")
        print(f"   TIME_WINDOWS: {config.TIME_WINDOWS}")
        print(f"   FUTURE_TIME_WINDOWS: {config.FUTURE_TIME_WINDOWS}")
        print(f"   DATA_YEAR_LIMIT: {config.DATA_YEAR_LIMIT}")
        
        # 导入main模块
        import main
        
        # 模拟命令行参数
        original_argv = sys.argv.copy()
        sys.argv = ['main.py']  # 模拟无参数运行
        
        try:
            # 测试参数解析
            args = main.parse_arguments()
            print(f"\n✅ 参数解析成功")
            print(f"   原始windows: {args.windows}")
            print(f"   原始future_windows: {args.future_windows}")
            
            # 测试参数验证
            args = main.validate_arguments(args)
            print(f"\n✅ 参数验证成功")
            print(f"   验证后windows: {args.windows}")
            print(f"   验证后future_windows: {args.future_windows}")
            print(f"   enable_future_analysis: {args.enable_future_analysis}")
            
            # 验证值是否正确
            if args.windows == config.TIME_WINDOWS:
                print(f"✅ 历史时间窗口设置正确")
            else:
                print(f"❌ 历史时间窗口设置错误")
                
            if args.future_windows == config.FUTURE_TIME_WINDOWS:
                print(f"✅ 未来时间窗口设置正确")
            else:
                print(f"❌ 未来时间窗口设置错误")
                
            if args.enable_future_analysis:
                print(f"✅ 未来分析默认启用")
            else:
                print(f"❌ 未来分析未默认启用")
                
        finally:
            # 恢复原始命令行参数
            sys.argv = original_argv
            
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_analyzer_with_new_settings():
    """使用新设置测试分析器"""
    print(f"\n🔧 测试分析器（使用新设置）")
    print("-" * 40)
    
    try:
        from src.data_loader import DataLoader
        from src.time_window_analyzer import TimeWindowAnalyzer
        import config
        
        # 加载少量数据
        loader = DataLoader()
        loader.file_list = loader.file_list[:3]  # 只加载3个文件
        data = loader.load_all_data()
        print(f"✅ 数据加载成功: {data.shape}")
        
        # 初始化分析器
        analyzer = TimeWindowAnalyzer(data)
        print(f"✅ 分析器初始化成功")
        print(f"   全部数据日期范围: {analyzer.date_range}")
        print(f"   过滤数据日期范围: {analyzer.filtered_date_range}")
        
        # 测试历史分析
        if analyzer.available_dates:
            test_date = analyzer.available_dates[-5]
            print(f"\n测试历史分析（日期: {test_date}）...")
            
            for window_days in config.TIME_WINDOWS:
                try:
                    result = analyzer.calculate_window_performance(test_date, window_days)
                    print(f"   历史{window_days}日窗口: {len(result)} 个板块")
                except Exception as e:
                    print(f"   历史{window_days}日窗口: 失败 - {str(e)}")
        
        # 测试未来分析
        if analyzer.filtered_available_dates:
            test_date = analyzer.filtered_available_dates[-10]
            print(f"\n测试未来分析（日期: {test_date}）...")
            
            for window_days in config.FUTURE_TIME_WINDOWS:
                try:
                    result = analyzer.calculate_future_window_performance(test_date, window_days)
                    print(f"   未来{window_days}日窗口: {len(result)} 个板块")
                except Exception as e:
                    print(f"   未来{window_days}日窗口: 失败 - {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始测试新的main.py功能")
    print("=" * 60)
    
    # 测试新的main.py
    if not test_new_main():
        print("\n💥 新main.py测试失败")
        return False
    
    # 测试分析器
    if not test_analyzer_with_new_settings():
        print("\n💥 分析器测试失败")
        return False
    
    print("\n🎉 所有测试通过！")
    print("✨ 修改已正确实现：")
    print("   - 未来分析功能默认启用")
    print("   - 历史和未来时间窗口使用配置文件设置")
    print("   - 用户可以直接运行 'python main.py' 获得完整分析")
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
