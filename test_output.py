#!/usr/bin/env python3
"""
简单的输出测试程序
"""

import os
from datetime import datetime
from pathlib import Path

def main():
    """测试输出功能"""
    print(f"测试开始时间: {datetime.now()}")
    
    # 创建output目录
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    # 创建一个测试文件
    test_file = output_dir / "test_output.txt"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(f"测试文件创建时间: {datetime.now()}\n")
        f.write("这是一个测试文件，用于验证程序是否能正常创建文件。\n")
    
    print(f"测试文件已创建: {test_file}")
    print(f"文件是否存在: {test_file.exists()}")
    
    # 列出output目录的内容
    print("output目录内容:")
    for item in output_dir.iterdir():
        print(f"  - {item.name}")

if __name__ == '__main__':
    main()
